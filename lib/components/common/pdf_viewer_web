// pdf_viewer_web.dart
import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;
import '../../theme/app_theme.dart';
import '../../theme/app_fonts.dart';
import '../../config/constants.dart';

class PDFViewerWeb extends StatelessWidget {
  final Uint8List fileBytes;
  final String fileName;

  const PDFViewerWeb({
    super.key,
    required this.fileBytes,
    required this.fileName,
  });

  @override
  Widget build(BuildContext context) {
    return PDFViewerWebScreen(
      fileBytes: fileBytes,
      fileName: fileName,
    );
  }
}

class PDFViewerWebScreen extends StatelessWidget {
  final Uint8List fileBytes;
  final String fileName;

  const PDFViewerWebScreen({
    super.key,
    required this.fileBytes,
    required this.fileName,
  });

  @override
  Widget build(BuildContext context) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final String viewerId = 'pdf-viewer-${DateTime.now().millisecondsSinceEpoch}';

    // Register the view factory
    ui_web.platformViewRegistry.registerViewFactory(
      viewerId,
      (int viewId) {
        final iframe = html.IFrameElement()
          ..src = url
          ..style.width = '100%'
          ..style.height = '100%'
          ..style.border = 'none'
          ..style.borderRadius = '12px';

        return iframe;
      },
    );

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      appBar: AppBar(
        title: Text(
          fileName,
          style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
        ),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.primaryTextColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
            html.Url.revokeObjectUrl(url);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.open_in_new),
            onPressed: () {
              html.window.open(url, '_blank');
            },
            tooltip: 'Open in New Tab',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _downloadPdf(fileBytes, fileName),
            tooltip: 'Download PDF',
          ),
        ],
      ),
      body: Container(
        margin: const EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: HtmlElementView(viewType: viewerId),
        ),
      ),
    );
  }

  void _downloadPdf(Uint8List fileBytes, String fileName) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);

    html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..click();

    html.Url.revokeObjectUrl(url);
  }
}

class PdfViewer {
  static void showPdf({
    required BuildContext context,
    required Uint8List fileBytes,
    required String fileName,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PDFViewerWeb(
          fileBytes: fileBytes,
          fileName: fileName,
        ),
      ),
    );
  }

  static void showPdfFullScreen({
    required BuildContext context,
    required Uint8List fileBytes,
    required String fileName,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PDFViewerWeb(
          fileBytes: fileBytes,
          fileName: fileName,
        ),
      ),
    );
  }
}