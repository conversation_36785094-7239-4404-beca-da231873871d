import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/config/app_strings.dart';
import 'package:neorevv/models/report_model.dart';
import 'package:neorevv/theme/app_theme.dart';
import '../dashboard/components/header.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '/theme/app_fonts.dart';
import 'components/report_sidebar.dart';
import 'components/pdf_preview_panel.dart';

class ReportScreen extends HookWidget {
  const ReportScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: reportsTab);
    final selectedReport = useState<ReportModel?>(null);

    // Sample report data matching the design
    final reports = useState<List<ReportModel>>([
      ReportModel(
        id: '1',
        title: 'Brokerage Sales Volume Report',
        icon: Icons.bar_chart,
        isSelected: true,
      ),
      ReportModel(
        id: '2',
        title: 'Commission Distribution Report',
        icon: Icons.pie_chart,
        isSelected: false,
      ),
      ReportModel(
        id: '3',
        title: 'Transaction Status Report',
        icon: Icons.list_alt,
        isSelected: false,
      ),
      ReportModel(
        id: '4',
        title: 'Listing Activity Report',
        icon: Icons.home_work,
        isSelected: false,
      ),
      ReportModel(
        id: '5',
        title: 'Escrow and Deposits Report',
        icon: Icons.account_balance,
        isSelected: false,
      ),
      ReportModel(
        id: '6',
        title: 'Closing Timeline Report',
        icon: Icons.timeline,
        isSelected: false,
      ),
      ReportModel(
        id: '7',
        title: 'Lead Source Effectiveness Report',
        icon: Icons.trending_up,
        isSelected: false,
      ),
      ReportModel(
        id: '8',
        title: 'Legal & Compliance Report',
        icon: Icons.gavel,
        isSelected: false,
      ),
      ReportModel(
        id: '9',
        title: 'Top Producers Report',
        icon: Icons.star,
        isSelected: false,
      ),
    ]);

    // Set initial selected report
    useEffect(() {
      selectedReport.value = reports.value.firstWhere((r) => r.isSelected);
      return null;
    }, []);

    void onReportSelected(ReportModel report) {
      // Update selection state
      final updatedReports = reports.value.map((r) {
        return r.copyWith(isSelected: r.id == report.id);
      }).toList();

      reports.value = updatedReports;
      selectedReport.value = report;
    }

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            Responsive.isMobile(context) ? 8 : defaultMargin,
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            0,
          ),
          child: Column(
            children: [
              header,
              const SizedBox(height: defaultPadding),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left Side - Reports Heading and Sidebar
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reports Heading
                        Container(
                          width: ResponsiveSizes.reportSidebarWidth(context),
                          padding: const EdgeInsets.only(
                            bottom: defaultPadding,
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                '$iconAssetpath/fi-rr-document.png',
                                width: 20,
                                height: 20,
                                color: AppTheme.black,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Reports',
                                style: AppFonts.semiBoldTextStyle(
                                  18,
                                  color: AppTheme.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Report Sidebar
                        Expanded(
                          child: ReportSidebar(
                            reports: reports.value,
                            onReportSelected: onReportSelected,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(width: defaultPadding),

                    // Right Panel - PDF Preview
                    Expanded(
                      child: selectedReport.value != null
                          ? PdfPreviewPanel(report: selectedReport.value!)
                          : const Center(
                              child: Text('Select a report to preview'),
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
