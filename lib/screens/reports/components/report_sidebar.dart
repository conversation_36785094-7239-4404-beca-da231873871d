import 'package:flutter/material.dart';
import 'package:neorevv/models/report_model.dart';
import 'package:neorevv/theme/app_theme.dart';
import '/config/constants.dart';
import '/theme/app_fonts.dart';

class ReportSidebar extends StatelessWidget {
  final List<ReportModel> reports;
  final Function(ReportModel) onReportSelected;

  const ReportSidebar({
    super.key,
    required this.reports,
    required this.onReportSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: Row(
              children: [
                Image.asset(
                  'assets/icons/fi-rr-document.png',
                  width: 20,
                  height: 20,
                  color: AppTheme.primaryTextColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Reports',
                  style: AppFonts.semiBoldTextStyle(
                    18,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Report List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: reports.length,
              itemBuilder: (context, index) {
                final report = reports[index];
                return _buildReportItem(report);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportItem(ReportModel report) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: report.isSelected ? AppTheme.primaryColor.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: report.isSelected 
            ? Border.all(color: AppTheme.primaryColor.withOpacity(0.3))
            : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: report.isSelected 
                ? AppTheme.primaryColor 
                : AppTheme.greyRoundBg,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            report.icon,
            size: 16,
            color: report.isSelected ? Colors.white : Colors.grey[600],
          ),
        ),
        title: Text(
          report.title,
          style: AppFonts.mediumTextStyle(
            13,
            color: report.isSelected 
                ? AppTheme.primaryColor 
                : AppTheme.primaryTextColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () => onReportSelected(report),
        hoverColor: AppTheme.primaryColor.withOpacity(0.05),
      ),
    );
  }
}
