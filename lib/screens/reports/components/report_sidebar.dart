import 'package:flutter/material.dart';
import 'package:neorevv/config/constants.dart';
import 'package:neorevv/models/report_model.dart';
import 'package:neorevv/theme/app_theme.dart';
import '/theme/app_fonts.dart';

class ReportSidebar extends StatelessWidget {
  final List<ReportModel> reports;
  final Function(ReportModel) onReportSelected;

  const ReportSidebar({
    super.key,
    required this.reports,
    required this.onReportSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ResponsiveSizes.reportSidebarWidth(context),
      height: 33,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.searchbarBg,
          // width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 12),
              itemCount: reports.length,
              itemBuilder: (context, index) {
                final report = reports[index];
                return _buildReportItem(report);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportItem(ReportModel report) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: AppTheme.searchbarBg,
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        leading: Image.asset(
          '$iconAssetpath/fi-rs-copy-alt.png',
          width: 12,
          height: 12,
          color: AppTheme.black,
        ),
        title: Text(
          report.title,
          style: AppFonts.mediumTextStyle(14, color: AppTheme.black),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () => onReportSelected(report),
        dense: true,
      ),
    );
  }
}
