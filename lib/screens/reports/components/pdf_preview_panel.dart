import 'package:flutter/material.dart';
import 'package:neorevv/models/report_model.dart';
import 'package:neorevv/theme/app_theme.dart';
import '/config/constants.dart';
import '/theme/app_fonts.dart';

class PdfPreviewPanel extends StatelessWidget {
  final ReportModel report;

  const PdfPreviewPanel({
    super.key,
    required this.report,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with title and buttons
          Container(
            padding: const EdgeInsets.all(defaultPadding),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey, width: 0.5),
              ),
            ),
            child: <PERSON>(
              children: [
                Text(
                  report.title,
                  style: AppFonts.semiBoldTextStyle(
                    18,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.picture_as_pdf, color: Colors.white, size: 12),
                          const SizedBox(width: 4),
                          Text(
                            '${report.title}.pdf',
                            style: AppFonts.regularTextStyle(10, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        'Preview',
                        style: AppFonts.mediumTextStyle(12, color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        side: BorderSide(color: Colors.grey[300]!),
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        'Download',
                        style: AppFonts.mediumTextStyle(12, color: Colors.grey[600]),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // PDF Preview Area
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(defaultPadding),
              decoration: BoxDecoration(
                border: Border.all(color: AppTheme.primaryColor, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // PDF Toolbar
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(6),
                        topRight: Radius.circular(6),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'PDF • 2.3k',
                          style: AppFonts.mediumTextStyle(12, color: Colors.white),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '• This brokerage financial report shows quarterly agent allocation, total agent count and overall',
                          style: AppFonts.regularTextStyle(11, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  
                  // PDF Content Area
                  Expanded(
                    child: Container(
                      color: Colors.grey[50],
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Mock PDF content
                            Container(
                              width: double.infinity,
                              height: 300,
                              margin: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Brokerage Sales Volume Report',
                                      style: AppFonts.boldTextStyle(16, color: AppTheme.primaryTextColor),
                                    ),
                                    const SizedBox(height: 20),
                                    Text(
                                      'This brokerage dashboard supports both office schedules and a dynamic order & delivery platform optimizing an intelligent market.',
                                      style: AppFonts.regularTextStyle(12, color: Colors.grey[600]),
                                    ),
                                    const SizedBox(height: 15),
                                    Text(
                                      'Accounting Ledger: This dashboard, the Platform Admin Panel has been restructured into the organization representation when comparing to both the original requirement document. Sales in accounting ledger for the standard brokerage.',
                                      style: AppFonts.regularTextStyle(12, color: Colors.grey[600]),
                                    ),
                                    const Spacer(),
                                    // Mock chart/diagram area
                                    Container(
                                      height: 100,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[100],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Center(
                                        child: Text(
                                          '[Chart/Diagram Area]',
                                          style: AppFonts.regularTextStyle(12, color: Colors.grey),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // PDF Controls
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.remove, size: 16),
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '550 x 502',
                            style: AppFonts.regularTextStyle(12, color: Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.add, size: 16),
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                        const SizedBox(width: 16),
                        Text('1', style: AppFonts.regularTextStyle(12, color: Colors.grey[600])),
                        const SizedBox(width: 8),
                        Text('/', style: AppFonts.regularTextStyle(12, color: Colors.grey[600])),
                        const SizedBox(width: 8),
                        Text('8', style: AppFonts.regularTextStyle(12, color: Colors.grey[600])),
                        const SizedBox(width: 16),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.fullscreen, size: 16),
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.refresh, size: 16),
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: const Icon(Icons.search, size: 16),
                          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
